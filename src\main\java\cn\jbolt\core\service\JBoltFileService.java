package cn.jbolt.core.service;

import cn.hutool.core.io.FileTypeUtil;
import cn.hutool.core.io.FileUtil;
import cn.jbolt.core.base.JBoltMsg;
import cn.jbolt.core.common.enums.JBoltSystemLogTargetType;
import cn.jbolt.core.consts.JBoltConst;
import cn.jbolt.core.kit.JBoltUserKit;
import cn.jbolt.core.model.JboltFile;
import cn.jbolt.core.service.base.JBoltBaseService;
import cn.jbolt.core.util.JBoltRealUrlUtil;
import cn.jbolt.core.util.JBoltUploadFileUtil;
import com.jfinal.core.JFinal;
import com.jfinal.kit.LogKit;
import com.jfinal.kit.Okv;
import com.jfinal.kit.Ret;
import com.jfinal.plugin.activerecord.Page;
import com.jfinal.upload.UploadFile;

import java.io.File;
import java.util.Collections;
import java.util.Date;
import java.util.List;

/**
 * 系统文件库管理
 *
 * @ClassName: JBoltFileService
 * @author: JFinal
 * @date: 2019年3月24日 上午12:40:27
 */
public class JBoltFileService extends JBoltBaseService<JboltFile> {
    private final JboltFile dao = new JboltFile().dao();

    @Override
    protected JboltFile dao() {
        return dao;
    }


    public Page<JboltFile> paginateAdminData(int pageNumber, int pageSize) {
        return paginate("id", "desc", pageNumber, pageSize);
    }

    /**
     * 保存图片
     *
     * @param file
     * @param uploadPath
     * @return
     */
    public Ret saveImageFile(UploadFile file, String uploadPath) {
        return saveFile(file, uploadPath, JboltFile.FILE_TYPE_IMAGE);
    }

    /**
     * 保存Excel
     *
     * @param file
     * @param uploadPath
     * @return
     */
    public Ret saveExcelFile(UploadFile file, String uploadPath) {
        return saveFile(file, uploadPath, JboltFile.FILE_TYPE_OFFICE);
    }

    /**
     * 保存音频
     *
     * @param file
     * @param uploadPath
     * @return
     */
    public Ret saveAudioFile(UploadFile file, String uploadPath) {
        return saveFile(file, uploadPath, JboltFile.FILE_TYPE_AUDIO);
    }

    /**
     * 保存视频
     *
     * @param file
     * @param uploadPath
     * @return
     */
    public Ret saveVideoFile(UploadFile file, String uploadPath) {
        return saveFile(file, uploadPath, JboltFile.FILE_TYPE_VEDIO);
    }

    /**
     * 保存其它附件
     *
     * @param file
     * @param uploadPath
     * @return
     */
    public Ret saveAttachmentFile(UploadFile file, String uploadPath) {
        return saveFile(file, uploadPath, JboltFile.FILE_TYPE_ATTACHMENT);
    }

    /**
     * 保存文件底层方法
     *
     * @param file
     * @param uploadPath
     * @param fileType
     * @return
     */
    public Ret saveFile(UploadFile file, String uploadPath, int fileType) {
        JboltFile jboltFile = saveJBoltFile(file, uploadPath, fileType);
        return jboltFile != null ? success(jboltFile.getLocalUrl(), "上传成功") : fail("上传失败");
    }

    /**
     * 保存文件底层方法
     *
     * @param file
     * @param uploadPath
     * @param fileType
     * @return
     */
    public JboltFile saveJBoltFile(UploadFile file, String uploadPath, int fileType) {
        String localPath = file.getUploadPath() + "/" + file.getFileName();
        String localUrl = FileUtil.normalize(JBoltRealUrlUtil.get(JFinal.me().getConstants().getBaseUploadPath() + JBoltConst.SLASH + uploadPath + JBoltConst.SLASH + file.getFileName()));
        localPath = FileUtil.normalize(localPath);
        JboltFile jboltFile = new JboltFile();
        jboltFile.setObjectUserId(JBoltUserKit.getUserId());
        jboltFile.setCreateTime(new Date());
        jboltFile.setFileName(file.getOriginalFileName());
        jboltFile.setFileType(fileType);
        jboltFile.setLocalPath(localPath);
        jboltFile.setLocalUrl(localUrl);
        File realFile = file.getFile();
        long fileSize = FileUtil.size(realFile);
        jboltFile.setFileSize((int) fileSize);
        String fileSuffix = FileTypeUtil.getType(realFile);
        jboltFile.setFileSuffix(fileSuffix);
        boolean success = jboltFile.save();
        return success ? jboltFile : null;
    }

    /**
     * 保存文件并保留原文件名（不重命名）
     *
     * @param file
     * @param uploadPath
     * @param fileType
     * @return
     */
    public JboltFile saveJBoltFileWithOriginalName(UploadFile file, String uploadPath, int fileType) {
        // 获取原始文件名
        String originalFileName = file.getOriginalFileName();

        // 构建保存路径，使用原始文件名
        String targetFileName = originalFileName;

        // 检查文件名是否已存在，如果存在则添加序号
        File targetDir = new File(file.getUploadPath());
        File targetFile = new File(targetDir, targetFileName);
        int counter = 1;
        String nameWithoutExt = FileUtil.getPrefix(originalFileName);
        String extension = FileUtil.getSuffix(originalFileName);

        while (targetFile.exists()) {
            targetFileName = nameWithoutExt + "(" + counter + ")" + (extension.isEmpty() ? "" : "." + extension);
            targetFile = new File(targetDir, targetFileName);
            counter++;
        }

        // 重命名上传的文件为目标文件名
        try {
            File uploadedFile = file.getFile();
            if (!uploadedFile.renameTo(targetFile)) {
                // 如果重命名失败，尝试复制文件
                FileUtil.copy(uploadedFile, targetFile, true);
                uploadedFile.delete();
            }
        } catch (Exception e) {
            LogKit.error("重命名文件失败: " + e.getMessage(), e);
            return null;
        }

        // 构建数据库记录
        String localPath = FileUtil.normalize(targetFile.getAbsolutePath());
        String localUrl = FileUtil.normalize(JBoltRealUrlUtil.get(JFinal.me().getConstants().getBaseUploadPath() + JBoltConst.SLASH + uploadPath + JBoltConst.SLASH + targetFileName));

        JboltFile jboltFile = new JboltFile();
        jboltFile.setObjectUserId(JBoltUserKit.getUserId());
        jboltFile.setCreateTime(new Date());
        jboltFile.setFileName(originalFileName); // 保存原始文件名
        jboltFile.setFileType(fileType);
        jboltFile.setLocalPath(localPath);
        jboltFile.setLocalUrl(localUrl);

        long fileSize = FileUtil.size(targetFile);
        jboltFile.setFileSize((int) fileSize);
        String fileSuffix = FileTypeUtil.getType(targetFile);
        jboltFile.setFileSuffix(fileSuffix);

        boolean success = jboltFile.save();
        return success ? jboltFile : null;
    }

    /**
     * 根据IDs获取数据
     *
     * @param ids
     * @return
     */
    public List<JboltFile> getListByIds(String ids) {
        if (notOk(ids)) {
            return Collections.emptyList();
        }
        return find(selectSql().in("id", ids));
    }


    @Override
    protected int systemLogTargetType() {
        return JBoltSystemLogTargetType.JBOLT_FILE.getValue();
    }

    /**
     * 删除数据 并删除物理路径指向的文件
     *
     * @param id
     * @param delRealFile
     * @return
     */
    public Ret deleteFileById(Object id, boolean delRealFile) {
        if (notOk(id)) {
            return fail(JBoltMsg.PARAM_ERROR);
        }
        JboltFile jboltFile = findById(id);
        if (jboltFile == null) {
            return fail(JBoltMsg.DATA_NOT_EXIST);
        }
        if (!delRealFile) {
            return SUCCESS;
        }
        boolean exist = FileUtil.exist(jboltFile.getLocalPath());
        if (!exist) {
            return SUCCESS;
        }
        boolean success = FileUtil.del(jboltFile.getLocalPath());
        return ret(success);
    }

    /**
     * 删除数据 并删除物理路径指向的文件
     *
     * @param filePath
     * @param delRealFile
     * @return
     */
    public Ret deleteFileByLocalPath(String filePath, boolean delRealFile) {
        if (notOk(filePath)) {
            return fail(JBoltMsg.PARAM_ERROR);
        }
        Ret ret = deleteBy(Okv.by("local_path", filePath));
        if (ret.isFail()) {
            return ret;
        }
        if (!delRealFile) {
            return SUCCESS;
        }
        boolean exist = FileUtil.exist(filePath);
        if (!exist) {
            return SUCCESS;
        }
        boolean success = JBoltUploadFileUtil.delete(filePath);
        return ret(success);
    }

    /**
     * 删除数据 并删除物理路径指向的文件
     *
     * @param url
     * @param delRealFile
     * @return
     */
    public Ret deleteFileByLocalUrl(String url, boolean delRealFile) {
        if (notOk(url)) {
            return fail(JBoltMsg.PARAM_ERROR);
        }
        List<JboltFile> jboltFiles = getCommonList(Okv.by("local_url", url));
        if (notOk(jboltFiles)) {
            return SUCCESS;
        }
        Ret ret = deleteBy(Okv.by("local_url", url));
        if (ret.isFail()) {
            return ret;
        }
        if (!delRealFile) {
            return SUCCESS;
        }
        for (JboltFile jboltFile : jboltFiles) {
            if (FileUtil.exist(jboltFile.getLocalPath())) {
                JBoltUploadFileUtil.delete(jboltFile.getLocalPath());
            }
        }
        return SUCCESS;

    }

}
