-- 更新邮件翻译表结构，支持分离的翻译内容
-- V16__update_email_translation_table.sql

-- 添加新字段来分别存储标题、内容和图片翻译
ALTER TABLE email_translation 
ADD COLUMN subject_original TEXT COMMENT '原始标题' AFTER translated_content,
ADD COLUMN subject_translated TEXT COMMENT '翻译标题' AFTER subject_original,
ADD COLUMN content_original LONGTEXT COMMENT '原始正文内容' AFTER subject_translated,
ADD COLUMN content_translated LONGTEXT COMMENT '翻译正文内容' AFTER content_original,
ADD COLUMN images_translation LONGTEXT COMMENT '图片翻译结果JSON格式' AFTER content_translated,
ADD COLUMN update_time DATETIME DEFAULT NULL COMMENT '更新时间' AFTER create_time;

-- 为新字段添加索引以提高查询性能
CREATE INDEX idx_email_translation_update_time ON email_translation(update_time);

-- 数据迁移：将现有的translated_content数据迁移到content_translated字段
UPDATE email_translation 
SET content_translated = translated_content,
    update_time = NOW()
WHERE translated_content IS NOT NULL AND translated_content != '';
