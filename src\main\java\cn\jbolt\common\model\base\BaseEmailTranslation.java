package cn.jbolt.common.model.base;
import cn.jbolt.core.model.base.JBoltBaseModel;
import cn.jbolt.core.gen.JBoltField;
import com.alibaba.fastjson.annotation.JSONField;
import com.alibaba.fastjson.serializer.ToStringSerializer;

/**
 * 邮件翻译表
 * Generated by <PERSON><PERSON><PERSON>, do not modify this file.
 */
@SuppressWarnings({"serial", "unchecked"})
public abstract class BaseEmailTranslation<M extends BaseEmailTranslation<M>> extends JBoltBaseModel<M>{
    public static final String DATASOURCE_CONFIG_NAME = "main";
    /***/
    public static final String ID = "id";
    /**邮件ID*/
    public static final String EMAIL_ID = "email_id";
    /***/
    public static final String ORIGINAL_CONTENT = "original_content";
    /***/
    public static final String TRANSLATED_CONTENT = "translated_content";
    /**原始标题*/
    public static final String SUBJECT_ORIGINAL = "subject_original";
    /**翻译标题*/
    public static final String SUBJECT_TRANSLATED = "subject_translated";
    /**原始正文内容*/
    public static final String CONTENT_ORIGINAL = "content_original";
    /**翻译正文内容*/
    public static final String CONTENT_TRANSLATED = "content_translated";
    /**图片翻译结果JSON格式*/
    public static final String IMAGES_TRANSLATION = "images_translation";
    /***/
    public static final String SCREENSHOT_PATH = "screenshot_path";
    /**创建时间*/
    public static final String CREATE_TIME = "create_time";
    /**更新时间*/
    public static final String UPDATE_TIME = "update_time";
	public M setId(java.lang.Long id) {
		set("id", id);
		return (M)this;
	}
	
	@JBoltField(name="id" ,columnName="id",type="Long", remark="ID", required=true, maxLength=19, fixed=0, order=1)
	@JSONField(serializeUsing= ToStringSerializer.class)
	public java.lang.Long getId() {
		return getLong("id");
	}

	/**
	 * 邮件ID
	 */
	public M setEmailId(java.lang.Long emailId) {
		set("email_id", emailId);
		return (M)this;
	}
	
	/**
	 * 邮件ID
	 */
	@JBoltField(name="emailId" ,columnName="email_id",type="Long", remark="邮件ID", required=true, maxLength=19, fixed=0, order=2)
	@JSONField(serializeUsing= ToStringSerializer.class)
	public java.lang.Long getEmailId() {
		return getLong("email_id");
	}

	public M setOriginalContent(java.lang.String originalContent) {
		set("original_content", originalContent);
		return (M)this;
	}
	
	@JBoltField(name="originalContent" ,columnName="original_content",type="String", remark="ORIGINALCONTENT", required=false, maxLength=2147483647, fixed=0, order=3)
	public java.lang.String getOriginalContent() {
		return getStr("original_content");
	}

	public M setTranslatedContent(java.lang.String translatedContent) {
		set("translated_content", translatedContent);
		return (M)this;
	}
	
	@JBoltField(name="translatedContent" ,columnName="translated_content",type="String", remark="TRANSLATEDCONTENT", required=false, maxLength=2147483647, fixed=0, order=4)
	public java.lang.String getTranslatedContent() {
		return getStr("translated_content");
	}

	/**
	 * 原始标题
	 */
	public M setSubjectOriginal(java.lang.String subjectOriginal) {
		set("subject_original", subjectOriginal);
		return (M)this;
	}

	/**
	 * 原始标题
	 */
	@JBoltField(name="subjectOriginal" ,columnName="subject_original",type="String", remark="原始标题", required=false, maxLength=65535, fixed=0, order=5)
	public java.lang.String getSubjectOriginal() {
		return getStr("subject_original");
	}

	/**
	 * 翻译标题
	 */
	public M setSubjectTranslated(java.lang.String subjectTranslated) {
		set("subject_translated", subjectTranslated);
		return (M)this;
	}

	/**
	 * 翻译标题
	 */
	@JBoltField(name="subjectTranslated" ,columnName="subject_translated",type="String", remark="翻译标题", required=false, maxLength=65535, fixed=0, order=6)
	public java.lang.String getSubjectTranslated() {
		return getStr("subject_translated");
	}

	/**
	 * 原始正文内容
	 */
	public M setContentOriginal(java.lang.String contentOriginal) {
		set("content_original", contentOriginal);
		return (M)this;
	}

	/**
	 * 原始正文内容
	 */
	@JBoltField(name="contentOriginal" ,columnName="content_original",type="String", remark="原始正文内容", required=false, maxLength=2147483647, fixed=0, order=7)
	public java.lang.String getContentOriginal() {
		return getStr("content_original");
	}

	/**
	 * 翻译正文内容
	 */
	public M setContentTranslated(java.lang.String contentTranslated) {
		set("content_translated", contentTranslated);
		return (M)this;
	}

	/**
	 * 翻译正文内容
	 */
	@JBoltField(name="contentTranslated" ,columnName="content_translated",type="String", remark="翻译正文内容", required=false, maxLength=2147483647, fixed=0, order=8)
	public java.lang.String getContentTranslated() {
		return getStr("content_translated");
	}

	/**
	 * 图片翻译结果JSON格式
	 */
	public M setImagesTranslation(java.lang.String imagesTranslation) {
		set("images_translation", imagesTranslation);
		return (M)this;
	}

	/**
	 * 图片翻译结果JSON格式
	 */
	@JBoltField(name="imagesTranslation" ,columnName="images_translation",type="String", remark="图片翻译结果JSON格式", required=false, maxLength=2147483647, fixed=0, order=9)
	public java.lang.String getImagesTranslation() {
		return getStr("images_translation");
	}

	public M setScreenshotPath(java.lang.String screenshotPath) {
		set("screenshot_path", screenshotPath);
		return (M)this;
	}
	
	@JBoltField(name="screenshotPath" ,columnName="screenshot_path",type="String", remark="SCREENSHOTPATH", required=false, maxLength=512, fixed=0, order=10)
	public java.lang.String getScreenshotPath() {
		return getStr("screenshot_path");
	}

	/**
	 * 创建时间
	 */
	public M setCreateTime(java.util.Date createTime) {
		set("create_time", createTime);
		return (M)this;
	}

	/**
	 * 创建时间
	 */
	@JBoltField(name="createTime" ,columnName="create_time",type="Date", remark="创建时间", required=true, maxLength=19, fixed=0, order=11)
	public java.util.Date getCreateTime() {
		return getDate("create_time");
	}

	/**
	 * 更新时间
	 */
	public M setUpdateTime(java.util.Date updateTime) {
		set("update_time", updateTime);
		return (M)this;
	}

	/**
	 * 更新时间
	 */
	@JBoltField(name="updateTime" ,columnName="update_time",type="Date", remark="更新时间", required=false, maxLength=19, fixed=0, order=12)
	public java.util.Date getUpdateTime() {
		return getDate("update_time");
	}

}

