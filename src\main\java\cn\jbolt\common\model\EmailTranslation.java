package cn.jbolt.common.model;

import cn.jbolt.common.model.base.BaseEmailTranslation;
import cn.jbolt.core.annotation.TableBind;
import cn.jbolt.core.base.JBoltIDGenMode;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 邮件翻译表
 * Generated by JBolt.
 */
@SuppressWarnings("serial")
@TableBind(dataSource = "main" , table = "email_translation" , primaryKey = "id" , idGenMode = JBoltIDGenMode.SNOWFLAKE)
public class EmailTranslation extends BaseEmailTranslation<EmailTranslation> {

    /**
     * 获取关联的邮件主题
     * @return 邮件主题
     */
    public String getEmailSubject() {
        return get("email_subject");
    }

    /**
     * 获取图片翻译结果列表
     * @return 图片翻译结果列表
     */
    public List<ImageTranslationResult> getImageTranslationResults() {
        String imagesTranslationJson = getImagesTranslation();
        if (StringUtils.isEmpty(imagesTranslationJson)) {
            return new ArrayList<>();
        }

        try {
            JSONArray jsonArray = JSON.parseArray(imagesTranslationJson);
            List<ImageTranslationResult> results = new ArrayList<>();
            for (int i = 0; i < jsonArray.size(); i++) {
                JSONObject jsonObject = jsonArray.getJSONObject(i);
                ImageTranslationResult result = new ImageTranslationResult();
                result.setImagePath(jsonObject.getString("imagePath"));
                result.setTranslatedContent(jsonObject.getString("translatedContent"));
                result.setOrder(jsonObject.getIntValue("order"));
                results.add(result);
            }
            return results;
        } catch (Exception e) {
            return new ArrayList<>();
        }
    }

    /**
     * 设置图片翻译结果列表
     * @param results 图片翻译结果列表
     */
    public void setImageTranslationResults(List<ImageTranslationResult> results) {
        if (results == null || results.isEmpty()) {
            setImagesTranslation(null);
            return;
        }

        JSONArray jsonArray = new JSONArray();
        for (ImageTranslationResult result : results) {
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("imagePath", result.getImagePath());
            jsonObject.put("translatedContent", result.getTranslatedContent());
            jsonObject.put("order", result.getOrder());
            jsonArray.add(jsonObject);
        }
        setImagesTranslation(jsonArray.toJSONString());
    }

    /**
     * 更新时间戳
     */
    public void updateTimestamp() {
        setUpdateTime(new Date());
    }

    /**
     * 图片翻译结果内部类
     */
    public static class ImageTranslationResult {
        private String imagePath;
        private String translatedContent;
        private int order;

        public String getImagePath() {
            return imagePath;
        }

        public void setImagePath(String imagePath) {
            this.imagePath = imagePath;
        }

        public String getTranslatedContent() {
            return translatedContent;
        }

        public void setTranslatedContent(String translatedContent) {
            this.translatedContent = translatedContent;
        }

        public int getOrder() {
            return order;
        }

        public void setOrder(int order) {
            this.order = order;
        }
    }
}

