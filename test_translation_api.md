# 邮件翻译API改造测试文档

## 改造内容总结

### 1. 后端改造
- **修改了 `getTranslationContent` 方法**：
  - 优先返回数据库中已存储的翻译标题 (`subject_translated`)
  - 如果没有翻译标题但有原标题，自动调用AI翻译标题
  - 翻译成功后自动保存到数据库
  - 返回格式新增 `subject` 字段直接提供翻译标题

- **新增了 `translateSubjectOnly` 私有方法**：
  - 专门用于单独翻译邮件标题
  - 使用与邮件翻译相同的AI提示词配置
  - 调用Kimi Moonshot模型进行翻译

### 2. 前端改造
- **修改了 `replyEmail.html` 中的翻译标题获取逻辑**：
  - 不再使用正则表达式从翻译内容中提取标题
  - 直接使用API返回的 `res.data.subject` 字段
  - 保留了原有的 `extractSubjectFromTranslation` 函数作为备用（已标记为弃用）

### 3. API返回格式变化

#### 原格式：
```json
{
  "state": "ok",
  "data": {
    "content": "【翻译标题】\n产品询价\n\n【翻译内容】\n...",
    "screenshots": [...]
  }
}
```

#### 新格式：
```json
{
  "state": "ok", 
  "data": {
    "content": "【翻译标题】\n产品询价\n\n【翻译内容】\n...",
    "subject": "产品询价",           // 新增：直接提供翻译标题
    "originalSubject": "Product Inquiry",  // 新增：原始标题
    "screenshots": [...]
  }
}
```

## 测试场景

### 场景1：已有翻译标题
- 数据库中已存在 `subject_translated` 字段
- 应该直接返回已存储的翻译标题

### 场景2：没有翻译标题，有原标题
- 数据库中没有 `subject_translated`，但邮件有 `subject`
- 应该自动调用AI翻译标题
- 翻译成功后保存到数据库并返回

### 场景3：没有标题
- 邮件本身没有标题
- 应该返回空或默认值

### 场景4：AI翻译失败
- 调用AI翻译标题时出现异常
- 应该返回原标题作为备用

## 优势

1. **性能提升**：不需要前端正则解析，直接获取翻译标题
2. **准确性提升**：避免正则表达式匹配错误
3. **缓存机制**：翻译过的标题会保存到数据库，避免重复翻译
4. **按需翻译**：只有在需要时才翻译标题，节省AI调用成本
5. **向后兼容**：保持原有API结构，只是新增字段

## 注意事项

1. 需要确保数据库表结构已更新（运行V16迁移脚本）
2. 第一次调用可能会稍慢（需要AI翻译标题）
3. 后续调用会很快（直接从数据库获取）
