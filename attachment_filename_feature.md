# 邮件附件文件名保留功能

## 功能概述

在回邮件的附件上传功能中新增了一个开关，允许用户选择是否保留原文件名。这个功能对于包含重要业务信息的文件名特别有用。

## 功能特点

### 🎯 核心功能
- **文件名保留开关**：用户可以选择是否保留原文件名
- **默认保留**：开关默认开启，保留原文件名
- **智能重命名**：如果文件名冲突，自动添加序号 (如：文件名(1).pdf)
- **用户偏好记忆**：系统会记住用户的选择偏好

### 🔧 技术实现

#### 前端改进
1. **UI界面**：
   - 在附件上传区域上方添加了文件名保留开关
   - 提供清晰的说明文字
   - 开关状态会影响上传提示信息

2. **用户体验**：
   - 上传成功后显示文件名处理状态
   - 保留原文件名的文件显示蓝色文件图标
   - 重命名的文件显示黄色随机图标
   - 用户偏好自动保存到localStorage

3. **状态反馈**：
   ```javascript
   // 上传成功提示
   保留原文件名：'文件名.pdf 上传成功（保留原文件名）'
   系统重命名：'文件名.pdf 上传成功（已重命名）'
   ```

#### 后端改进
1. **Controller层**：
   - `uploadAttachment` 方法新增 `keepOriginalFileName` 参数
   - 根据参数选择不同的文件保存策略
   - 返回文件处理状态信息

2. **Service层**：
   - 新增 `saveJBoltFileWithOriginalName` 方法
   - 智能处理文件名冲突（添加序号）
   - 保持原有 `saveJBoltFile` 方法不变，确保兼容性

### 📋 使用场景

#### 适合保留原文件名的情况：
- 合同文件：`合同-张三-20241205.pdf`
- 报价单：`产品报价单-客户A-v2.xlsx`
- 技术文档：`API接口文档-v1.2.docx`
- 图纸文件：`产品设计图-最终版.dwg`

#### 适合使用系统文件名的情况：
- 临时文件或测试文件
- 文件名包含特殊字符或过长
- 批量上传相似文件名的文件
- 对文件名没有特殊要求的场景

### 🎨 界面展示

```html
<!-- 文件名保留开关 -->
<div class="form-check form-check-inline">
    <input class="form-check-input" type="checkbox" id="keepOriginalFileName" checked>
    <label class="form-check-label" for="keepOriginalFileName">
        <i class="fa fa-file-text text-info"></i> 保留原文件名
    </label>
    <small class="text-muted ms-2">（文件名包含重要信息时建议保留）</small>
</div>
```

### 🔄 文件名冲突处理

当保留原文件名时，如果遇到同名文件，系统会自动处理：

```
原文件名：报价单.pdf
如果冲突：
- 第1个冲突：报价单(1).pdf  
- 第2个冲突：报价单(2).pdf
- 以此类推...
```

### 📊 API接口变化

#### 请求参数新增：
```javascript
FormData.append('keepOriginalFileName', true/false);
```

#### 响应数据新增：
```json
{
  "state": "ok",
  "data": {
    "id": "123456789",
    "fileName": "报价单.pdf",
    "originalFileName": "报价单.pdf", 
    "keepOriginalFileName": true,
    "fileSize": 1024000,
    "localPath": "/uploads/2024/12/05/报价单.pdf",
    "localUrl": "/uploads/2024/12/05/报价单.pdf"
  }
}
```

### 🚀 优势特点

1. **业务友好**：保留有意义的文件名，便于识别和管理
2. **灵活选择**：用户可以根据需要选择保留或重命名
3. **智能处理**：自动处理文件名冲突，无需用户干预
4. **向后兼容**：不影响现有功能，平滑升级
5. **用户体验**：记住用户偏好，减少重复操作

### 🔧 配置说明

- **默认设置**：开关默认开启（保留原文件名）
- **用户偏好**：选择会自动保存到浏览器本地存储
- **文件大小限制**：保持30MB限制不变
- **支持格式**：支持所有文件格式，包括HEIC/HEIF

这个功能特别适合处理包含重要业务信息的文件，如合同、报价单、技术文档等，确保文件名的业务含义得到保留。
