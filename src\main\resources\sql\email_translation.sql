-- 邮件翻译表
CREATE TABLE IF NOT EXISTS `email_translation` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `email_id` bigint(20) NOT NULL COMMENT '邮件ID',
  `original_content` longtext COMMENT '原始内容',
  `translated_content` longtext COMMENT '翻译内容',
  `subject_original` text COMMENT '原始标题',
  `subject_translated` text COMMENT '翻译标题',
  `content_original` longtext COMMENT '原始正文内容',
  `content_translated` longtext COMMENT '翻译正文内容',
  `images_translation` longtext COMMENT '图片翻译结果JSON格式',
  `screenshot_path` varchar(512) DEFAULT NULL COMMENT '截图路径',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_email_id` (`email_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='邮件翻译表';
