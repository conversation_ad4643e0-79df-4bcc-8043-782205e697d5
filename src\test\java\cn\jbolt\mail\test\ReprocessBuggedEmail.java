package cn.jbolt.mail.test;

import cn.jbolt.common.model.EmailAccount;
import cn.jbolt.common.model.EmailMessages;
import cn.jbolt.core.kit.JBoltUserKit;
import cn.jbolt.mail.gpt.client.EmailClient;
import cn.jbolt.mail.gpt.client.EmailClientPool;
import com.jfinal.kit.LogKit;
import com.jfinal.plugin.activerecord.Db;

/**
 * 重新处理有问题的邮件
 * 用于测试修复后的邮件解析逻辑
 */
public class ReprocessBuggedEmail {
    
    public static void main(String[] args) {
        System.out.println("=== 重新处理有问题的邮件 ===");
        
        // 目标邮件ID
        Long emailId = 1952379933986582528L;
        
        try {
            // 1. 查询邮件信息
            EmailMessages email = new EmailMessages().dao().findById(emailId);
            if (email == null) {
                System.out.println("❌ 邮件不存在: " + emailId);
                return;
            }
            
            System.out.println("📧 找到邮件:");
            System.out.println("  ID: " + email.getId());
            System.out.println("  主题: " + email.getSubject());
            System.out.println("  发件人: " + email.getFromAddress());
            System.out.println("  邮箱账号: " + email.getEmailAccount());
            System.out.println("  是否有附件: " + email.getHasAttachments());
            System.out.println("  获取状态: " + email.getFetchStatus());
            
            // 2. 查询邮箱账号
            EmailAccount account = new EmailAccount().dao().findById(email.getAccountId());
            if (account == null) {
                System.out.println("❌ 邮箱账号不存在: " + email.getAccountId());
                return;
            }
            
            System.out.println("📮 邮箱账号: " + account.getUsername());
            
            // 3. 查询当前附件记录
            int attachmentCount = Db.queryInt("SELECT COUNT(*) FROM email_attachments WHERE email_id = ?", emailId);
            System.out.println("📎 当前附件记录数: " + attachmentCount);
            
            // 4. 显示当前邮件内容长度
            String contentHtml = email.getContentHtml();
            String contentText = email.getContentText();
            System.out.println("📄 HTML内容长度: " + (contentHtml != null ? contentHtml.length() : 0));
            System.out.println("📄 Text内容长度: " + (contentText != null ? contentText.length() : 0));
            
            // 检查是否包含PDF内容
            if (contentHtml != null && contentHtml.contains("%PDF-")) {
                System.out.println("⚠️  检测到HTML内容中包含PDF数据");
            }
            if (contentText != null && contentText.contains("%PDF-")) {
                System.out.println("⚠️  检测到Text内容中包含PDF数据");
            }
            
            // 5. 重新获取邮件
            System.out.println("\n🔄 开始重新获取邮件...");
            
            EmailClientPool clientPool = EmailClientPool.getInstance();
            EmailClient client = clientPool.getClient(account, EmailClient.Mode.COMPLETE_MODE);
            
            boolean success = client.fetchEmailByEmailMessages(email);
            
            if (success) {
                System.out.println("✅ 重新获取邮件成功");
                
                // 6. 重新查询邮件信息
                email = new EmailMessages().dao().findById(emailId);
                System.out.println("\n📧 重新获取后的邮件信息:");
                System.out.println("  主题: " + email.getSubject());
                System.out.println("  是否有附件: " + email.getHasAttachments());
                System.out.println("  获取状态: " + email.getFetchStatus());
                
                // 7. 查询新的附件记录
                int newAttachmentCount = Db.queryInt("SELECT COUNT(*) FROM email_attachments WHERE email_id = ?", emailId);
                System.out.println("📎 新的附件记录数: " + newAttachmentCount);
                
                // 8. 显示新的邮件内容长度
                String newContentHtml = email.getContentHtml();
                String newContentText = email.getContentText();
                System.out.println("📄 新HTML内容长度: " + (newContentHtml != null ? newContentHtml.length() : 0));
                System.out.println("📄 新Text内容长度: " + (newContentText != null ? newContentText.length() : 0));
                
                // 检查是否还包含PDF内容
                if (newContentHtml != null && newContentHtml.contains("%PDF-")) {
                    System.out.println("❌ HTML内容中仍包含PDF数据");
                } else {
                    System.out.println("✅ HTML内容中不再包含PDF数据");
                }
                
                if (newContentText != null && newContentText.contains("%PDF-")) {
                    System.out.println("❌ Text内容中仍包含PDF数据");
                } else {
                    System.out.println("✅ Text内容中不再包含PDF数据");
                }
                
                // 9. 显示附件信息
                if (newAttachmentCount > 0) {
                    System.out.println("\n📎 附件列表:");
                    var attachments = Db.find("SELECT * FROM email_attachments WHERE email_id = ?", emailId);
                    for (var attachment : attachments) {
                        System.out.println("  - " + attachment.getStr("filename") + 
                                         " (大小: " + attachment.getLong("size") + " bytes)");
                    }
                }
                
            } else {
                System.out.println("❌ 重新获取邮件失败");
            }
            
        } catch (Exception e) {
            System.out.println("❌ 处理失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
