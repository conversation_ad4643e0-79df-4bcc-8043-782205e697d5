# 邮件删除和重发功能

## 功能概述

在邮件查看页面（`viewEmail.html`）新增了两个重要功能按钮：
- **删除邮件**：允许用户删除当前查看的邮件
- **邮件重发**：允许用户重新发送已发送的邮件

## 功能特点

### 🗑️ 删除邮件功能

#### 界面特性
- **按钮样式**：红色危险按钮，清晰标识删除操作
- **确认对话框**：防止误删，显示"确定要删除这封邮件吗？删除后无法恢复！"
- **加载状态**：删除过程中显示加载动画和"删除中..."文字
- **智能跳转**：删除成功后自动处理页面跳转

#### 技术实现
```javascript
function deleteEmail(emailId) {
    layer.confirm('确定要删除这封邮件吗？删除后无法恢复！', {
        icon: 3,
        title: '删除确认',
        btn: ['确定删除', '取消']
    }, function(index) {
        // 删除逻辑
    });
}
```

#### 后端接口
- **URL**: `admin/emailMessages/deleteEmail`
- **方法**: `POST`
- **参数**: `emailId` (邮件ID)
- **返回**: 标准JSON响应格式

### 📧 邮件重发功能

#### 界面特性
- **按钮样式**：蓝色信息按钮，表示重发操作
- **确认对话框**：显示"确定要重新发送这封邮件吗？"
- **加载状态**：重发过程中显示加载动画和"重发中..."文字
- **状态反馈**：重发成功或失败的明确提示

#### 功能限制
- **仅限已发送邮件**：只有 `is_sent = true` 的邮件才能重发
- **账号验证**：验证发件邮箱账号是否存在且有效
- **内容保持**：保持原邮件的主题、内容、收件人等信息

#### 技术实现
```javascript
function resendEmail(emailId) {
    layer.confirm('确定要重新发送这封邮件吗？', {
        icon: 3,
        title: '重发确认',
        btn: ['确定重发', '取消']
    }, function(index) {
        // 重发逻辑
    });
}
```

#### 后端接口
- **URL**: `admin/emailMessages/resendEmail`
- **方法**: `POST`
- **参数**: `emailId` (邮件ID)
- **返回**: 标准JSON响应格式

#### 附件和图片处理
- **普通附件**：从 `email_attachments` 表获取已下载的附件文件
- **内联图片**：EmailsService会自动处理邮件内容中的图片引用
- **CID处理**：系统会自动将内容中的图片转换为正确的CID格式
- **文件验证**：只有存在的文件才会被添加到重发邮件中

## 界面布局

### 按钮位置
按钮位于邮件查看页面顶部的操作按钮行中，按照以下顺序排列：
```
[打印] [显示模式] [夜间模式] [AI排版] [翻译] [回复] [回复全部] [转发] [邮件重发] [删除邮件] [强制重新接收]
```

### 按钮样式
```html
<!-- 邮件重发按钮 -->
<button class="btn btn-info btn-action" onclick="resendEmail('#(email.id)')" title="重新发送此邮件">
    <i class="fa fa-paper-plane"></i>
    <span class="btn-text">邮件重发</span>
</button>

<!-- 删除邮件按钮 -->
<button class="btn btn-danger btn-action" onclick="deleteEmail('#(email.id)')" title="删除此邮件">
    <i class="fa fa-trash"></i>
    <span class="btn-text">删除邮件</span>
</button>
```

## 用户体验优化

### 🎨 视觉反馈
- **悬停效果**：按钮悬停时有轻微上移和阴影效果
- **加载动画**：操作过程中显示旋转的加载图标
- **状态变化**：按钮文字和图标根据操作状态动态变化

### 🔒 安全机制
- **二次确认**：所有危险操作都需要用户确认
- **权限验证**：后端验证用户是否有权限执行操作
- **错误处理**：完善的错误提示和状态恢复

### 📱 响应式设计
- **移动端适配**：按钮在小屏幕设备上正常显示
- **触摸优化**：适合触摸操作的按钮大小和间距

## 后端实现详情

### 删除邮件逻辑
```java
public void deleteEmail() {
    Long emailId = getLong("emailId");
    // 验证邮件存在性
    EmailMessages email = service.findById(emailId);
    // 执行软删除
    boolean success = service.deleteEmail(emailId);
    // 返回结果
}
```

### 重发邮件逻辑
```java
public void resendEmail() {
    Long emailId = getLong("emailId");
    // 获取原邮件信息
    EmailMessages originalEmail = service.findById(emailId);
    // 验证是否为已发送邮件
    if (!Boolean.TRUE.equals(originalEmail.getIsSent())) {
        renderJsonFail("只能重发已发送的邮件");
        return;
    }
    // 获取原邮件的附件文件
    List<File> attachmentFiles = getOriginalEmailAttachments(originalEmail.getId());
    // 重新发送邮件 - EmailsService会自动处理内容中的图片CID引用
    Ret result = emailsService.sendEmail(fromEmail, toEmail, ccEmail, subject,
        originalContent, null, attachmentFiles);
}

private List<File> getOriginalEmailAttachments(Long emailId) {
    // 获取普通附件（不包括内联图片）
    List<EmailAttachments> attachments = new EmailAttachments().dao().find(
        "SELECT * FROM email_attachments WHERE email_id = ? AND status = 2 AND (cid IS NULL OR cid = '')",
        emailId);
    // 转换为File对象并验证文件存在性
}
```
```

## 错误处理

### 常见错误场景
1. **邮件不存在**：提示"邮件不存在"
2. **权限不足**：提示相应的权限错误
3. **网络异常**：提示"操作失败，请稍后重试"
4. **重发限制**：提示"只能重发已发送的邮件"

### 状态恢复
- 操作失败时自动恢复按钮原始状态
- 清除加载动画和临时文字
- 保持用户界面的一致性

## 使用场景

### 删除邮件适用场景
- 清理不需要的邮件
- 删除测试邮件
- 整理邮箱空间

### 重发邮件适用场景
- 邮件发送失败后重试
- 向新的收件人发送相同内容
- 确保重要邮件送达

## 注意事项

1. **删除操作不可逆**：邮件删除后无法恢复，请谨慎操作
2. **重发会产生新邮件**：重发操作会创建新的邮件记录
3. **权限控制**：确保用户只能操作自己有权限的邮件
4. **性能考虑**：大量操作时注意系统性能影响

这两个功能大大提升了邮件管理的便利性，让用户能够更高效地处理邮件。
