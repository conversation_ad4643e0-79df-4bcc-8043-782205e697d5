package cn.jbolt.mail.gpt.parser;

import static cn.jbolt.common.util.StringKit.addMapValue;

import java.io.Closeable;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.UnsupportedEncodingException;
import java.util.*;
import java.util.stream.Collectors;

import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;

import cn.jbolt.common.model.EmailAttachments;
import cn.jbolt.common.model.EmailMessages;
import cn.jbolt.common.util.FileKit;
import com.jfinal.plugin.activerecord.Db;
import jakarta.mail.Address;
import jakarta.mail.BodyPart;
import jakarta.mail.Folder;
import jakarta.mail.Message;
import jakarta.mail.MessagingException;
import jakarta.mail.Multipart;
import jakarta.mail.Part;
import jakarta.mail.internet.InternetAddress;
import jakarta.mail.internet.MimeMessage;
import jakarta.mail.internet.MimePart;
import jakarta.mail.internet.MimeUtility;
import jakarta.mail.internet.MimeUtility;

/**
 * Jakarta Mail 邮件解析器
 */
public class EmailParserJakarta extends EmailParserAbstract {

    @Override
    public String parseMessageIdFull(Object message) {
        if (!(message instanceof Message)) {
            throw new IllegalArgumentException("Message must be a Jakarta Mail Message");
        }

        Message mailMessage = (Message) message;
        try {
            String[] headers = mailMessage.getHeader("Message-ID");
            if (headers != null && headers.length > 0) {
                return headers[0];
            }
        } catch (Exception e) {
            LOG.warn("无法使用Jakarta Mail解析邮件Message-ID: {}", e.getMessage());
        }
        return StringUtils.EMPTY;
    }

    @Override
    public String parseSubject(Object message) {
        if (!(message instanceof Message)) {
            throw new IllegalArgumentException("Message must be a Jakarta Mail Message");
        }

        Message mailMessage = (Message) message;
        try {
            String subject = mailMessage.getSubject();
            if (subject != null) {
                return subject;
            }
        } catch (MessagingException e) {
            LOG.warn("无法使用Jakarta Mail解析邮件主题: {}", e.getMessage());
        }

        // 尝试从头信息获取主题
        try {
            if (mailMessage instanceof MimeMessage) {
                MimeMessage mimeMessage = (MimeMessage) mailMessage;
                String[] headers = mimeMessage.getHeader("Subject");
                if (headers != null && headers.length > 0) {
                    String rawSubject = headers[0];
                    if (rawSubject != null && !rawSubject.trim().isEmpty()) {
                        // 尝试解码可能的编码主题
                        try {
                            String decodedSubject = jakarta.mail.internet.MimeUtility.decodeText(rawSubject);
                            if (decodedSubject != null && !decodedSubject.trim().isEmpty()) {
                                return decodedSubject;
                            }
                        } catch (Exception decodeEx) {
                            LOG.debug("解码主题失败，使用原始主题: {}", decodeEx.getMessage());
                            return rawSubject;
                        }
                        return rawSubject;
                    }
                }
            }
        } catch (Exception ex) {
            LOG.warn("从头信息获取主题失败: {}", ex.getMessage());
        }

        // 尝试其他可能的主题头
        try {
            if (mailMessage instanceof MimeMessage) {
                MimeMessage mimeMessage = (MimeMessage) mailMessage;
                // 尝试其他可能的主题头字段
                String[] alternativeHeaders = {"subject", "SUBJECT", "Subject"};
                for (String headerName : alternativeHeaders) {
                    String[] headers = mimeMessage.getHeader(headerName);
                    if (headers != null && headers.length > 0 && headers[0] != null && !headers[0].trim().isEmpty()) {
                        try {
                            return jakarta.mail.internet.MimeUtility.decodeText(headers[0]);
                        } catch (Exception e) {
                            return headers[0];
                        }
                    }
                }
            }
        } catch (Exception ex) {
            LOG.warn("尝试其他主题头失败: {}", ex.getMessage());
        }

        LOG.warn("所有主题解析方法都失败，返回空字符串");
        return StringUtils.EMPTY;
    }

    @Override
    public String parsePersonal(Object message) {
        if (!(message instanceof Message)) {
            throw new IllegalArgumentException("Message must be a Jakarta Mail Message");
        }

        Message mailMessage = (Message) message;
        try {
            Address[] addresses = mailMessage.getFrom();
            if (addresses != null && addresses.length > 0) {
                if (addresses[0] instanceof InternetAddress) {
                    return ((InternetAddress) addresses[0]).getPersonal();
                } else {
                    return addresses[0].toString();
                }
            }
        } catch (MessagingException e) {
            LOG.warn("无法使用Jakarta Mail解析发件人: " + e.getMessage());

            // 尝试从头信息获取发件人
            try {
                if (mailMessage instanceof MimeMessage) {
                    MimeMessage mimeMessage = (MimeMessage) mailMessage;
                    String[] headers = mimeMessage.getHeader("From");
                    if (headers != null && headers.length > 0) {
                        return headers[0];
                    }
                }
            } catch (Exception ex) {
                LOG.warn("从头信息获取发件人失败: " + ex.getMessage());
            }
        }

        return StringUtils.EMPTY;
    }

    @Override
    public String parseFrom(Object message) {
        if (!(message instanceof Message)) {
            throw new IllegalArgumentException("Message must be a Jakarta Mail Message");
        }

        Message mailMessage = (Message) message;
        try {
            Address[] addresses = mailMessage.getFrom();
            if (addresses != null && addresses.length > 0) {
                if (addresses[0] instanceof InternetAddress) {
                    return ((InternetAddress) addresses[0]).getAddress();
                } else {
                    return addresses[0].toString().toLowerCase();
                }
            }
        } catch (MessagingException e) {
            LOG.warn("无法使用Jakarta Mail解析发件人: " + e.getMessage());

            // 尝试从头信息获取发件人
            try {
                if (mailMessage instanceof MimeMessage) {
                    MimeMessage mimeMessage = (MimeMessage) mailMessage;
                    String[] headers = mimeMessage.getHeader("From");
                    if (headers != null && headers.length > 0) {
                        return headers[0];
                    }
                }
            } catch (Exception ex) {
                LOG.warn("从头信息获取发件人失败: " + ex.getMessage());
            }
        }

        return StringUtils.EMPTY;
    }

    @Override
    public List<String> parseTo(Object message) {
        return parseRecipients(message, Message.RecipientType.TO);
    }

    @Override
    public List<String> parseCC(Object message) {
        return parseRecipients(message, Message.RecipientType.CC);
    }

    @Override
    public List<String> parseBCC(Object message) {
        return parseRecipients(message, Message.RecipientType.BCC);
    }

    private List<String> parseRecipients(Object message, Message.RecipientType type) {
        if (!(message instanceof Message)) {
            throw new IllegalArgumentException("Message must be a Jakarta Mail Message");
        }

        Message mailMessage = (Message) message;
        try {
            Address[] addresses = mailMessage.getRecipients(type);
            if (addresses != null && addresses.length > 0) {
                return Arrays.stream(addresses)
                        .map(address -> {
                            if (address instanceof InternetAddress) {
                                return ((InternetAddress) address).getAddress();
                            } else {
                                return address.toString().toLowerCase();
                            }
                        })
                        .collect(Collectors.toList());
            }
        } catch (MessagingException e) {
            LOG.warn("无法使用Jakarta Mail解析收件人: {}", e.getMessage());

            // 尝试从头信息获取收件人
            try {
                if (mailMessage instanceof MimeMessage) {
                    MimeMessage mimeMessage = (MimeMessage) mailMessage;
                    String headerName = null;
                    if (type == Message.RecipientType.TO) {
                        headerName = "To";
                    } else if (type == Message.RecipientType.CC) {
                        headerName = "Cc";
                    } else if (type == Message.RecipientType.BCC) {
                        headerName = "Bcc";
                    }

                    if (headerName != null) {
                        String[] headers = mimeMessage.getHeader(headerName);
                        if (headers != null && headers.length > 0) {
                            List<String> recipients = new ArrayList<>();
                            for (String header : headers) {
                                recipients.add(header.toLowerCase());
                            }
                            return recipients;
                        }
                    }
                }
            } catch (Exception ex) {
                LOG.warn("从头信息获取收件人失败: {}", ex.getMessage());
            }
        }

        return Collections.emptyList();
    }

    @Override
    public String parseContentType(Object message) {
        if (!(message instanceof Message)) {
            throw new IllegalArgumentException("Message must be a Jakarta Mail Message");
        }

        Message mailMessage = (Message) message;
        try {
            return mailMessage.getContentType();
        } catch (MessagingException e) {
            LOG.warn("无法使用Jakarta Mail解析内容类型: {}", e.getMessage());

            // 尝试从头信息获取内容类型
            try {
                if (mailMessage instanceof MimeMessage) {
                    MimeMessage mimeMessage = (MimeMessage) mailMessage;
                    String[] headers = mimeMessage.getHeader("Content-Type");
                    if (headers != null && headers.length > 0) {
                        return headers[0];
                    }
                }
            } catch (Exception ex) {
                LOG.warn("从头信息获取内容类型失败: {}", ex.getMessage());
            }

            return "text/plain"; // 默认内容类型
        }
    }

    @Override
    public boolean hasAttachments(Object message) {
        if (!(message instanceof Message)) {
            throw new IllegalArgumentException("Message must be a Jakarta Mail Message");
        }

        Message mailMessage = (Message) message;
        try {
            // 检查内容类型
            String contentType = parseContentType(message);

            // 特殊处理系统退信邮件
            String subject = parseSubject(message);
            if (subject != null && (
                    subject.contains("系统退信") ||
                            subject.contains("The email is returned") ||
                            subject.contains("Delivery Status Notification") ||
                            subject.contains("Mail delivery failed") ||
                            subject.contains("Undelivered Mail Returned") ||
                            subject.contains("Undeliverable") ||
                            subject.contains("Delivery Failure") ||
                            subject.contains("failed delivery") ||
                            subject.contains("Non-delivery") ||
                            subject.contains("Returned mail") ||
                            subject.contains("Failure Notice"))) {

                LOG.info("检测到系统退信邮件，假设有附件: " + subject);
                return true;
            }

            // 特殊处理特定类型的邮件
            if (contentType != null && (
                    contentType.contains("multipart/report") ||
                            contentType.contains("message/delivery-status") ||
                            contentType.contains("message/rfc822") ||
                            contentType.contains("message/disposition-notification") ||
                            (contentType.contains("multipart/mixed") && subject != null && subject.toLowerCase().contains("undeliverable")))) {

                LOG.info("检测到特殊内容类型邮件，假设有附件: " + contentType);
                return true;
            }

            // 检查是否是多部分邮件
            if (contentType != null && contentType.toLowerCase().startsWith("multipart/")) {
                try {
                    Object content = getContentWithoutMarkingAsRead(mailMessage);
                    if (content instanceof Multipart) {
                        return hasAttachmentInMultipart((Multipart) content);
                    }
                } catch (Exception e) {
                    LOG.warn("检查多部分邮件附件失败: {}", e.getMessage());

                    // 如果无法解析多部分内容，检查内容类型
                    return contentType.toLowerCase().contains("multipart/mixed") ||
                            contentType.toLowerCase().contains("multipart/related");
                }
            }

            // 检查Content-Disposition头
            try {
                if (mailMessage instanceof MimeMessage) {
                    MimeMessage mimeMessage = (MimeMessage) mailMessage;
                    String[] headers = mimeMessage.getHeader("Content-Disposition");
                    if (headers != null && headers.length > 0) {
                        String disposition = headers[0];
                        if (disposition.toLowerCase().contains("attachment")) {
                            return true;
                        }
                    }
                }
            } catch (Exception e) {
                LOG.warn("检查Content-Disposition头失败: {}", e.getMessage());
            }

            return false;
        } catch (Exception e) {
            LOG.warn("检查附件失败: {}", e.getMessage());

            // 如果无法确定，保守起见返回true
            return true;
        }
    }

    private boolean hasAttachmentInMultipart(Multipart multipart) throws MessagingException, IOException {
        int count = multipart.getCount();

        for (int i = 0; i < count; i++) {
            BodyPart bodyPart = multipart.getBodyPart(i);
            String disposition = bodyPart.getDisposition();

            // 检查是否是附件 - 只有ATTACHMENT才算真正的附件
            if (disposition != null && disposition.equalsIgnoreCase(Part.ATTACHMENT)) {
                return true;
            } else if (bodyPart.getContentType().toLowerCase().startsWith("multipart/")) {
                // 递归检查嵌套的多部分内容
                Object content = getBodyPartContentWithoutMarkingAsRead(bodyPart);
                if (content instanceof Multipart) {
                    if (hasAttachmentInMultipart((Multipart) content)) {
                        return true;
                    }
                }
            }
        }

        return false;
    }

    @Override
    public void parseAttachments(Object message, EmailMessages emailMessages) {
        // 强制调试输出 - 确认方法被调用
        System.out.println("🔥 DEBUG: parseAttachments 被调用！邮件ID: " + emailMessages.getId());
        System.out.println("🔥 DEBUG: 邮件主题: " + emailMessages.getSubject());

        if (!(message instanceof Message)) {
            throw new IllegalArgumentException("Message must be a Jakarta Mail Message");
        }

        Message mailMessage = (Message) message;

        try {
            String contentType = mailMessage.getContentType();
            System.out.println("🔥 DEBUG: 邮件Content-Type: " + contentType);
            if (contentType == null) {
                addEmailMessagesContentHtml(getContentWithoutMarkingAsRead(mailMessage), emailMessages, "将HTML内容添加到邮件正文{} {} {}", emailMessages.getAccountId(), emailMessages.getId());
                return;
            }
            if (contentType.toLowerCase().startsWith("multipart/")) {
                System.out.println("🔥 DEBUG: 检测到多部分邮件，开始解析附件...");
                Object content = getContentWithoutMarkingAsRead(mailMessage);
                if (content instanceof Multipart) {
                    System.out.println("🔥 DEBUG: 确认为Multipart对象，调用parseAttachmentsFromMultipart");
                    parseAttachmentsFromMultipart((Multipart) content, emailMessages);
                } else {
                    System.out.println("🔥 DEBUG: 警告 - Content-Type为multipart但实际对象不是Multipart: " +
                        (content != null ? content.getClass().getName() : "null"));
                }
            } else {
                if (contentType.toLowerCase().contains("text/plain")) {
                    addEmailMessagesContentText(getContentWithoutMarkingAsRead(mailMessage), emailMessages, "将HTML内容添加到邮件正文{} {} {}", emailMessages.getAccountId(), emailMessages.getId());
                    return;
                }
                if (contentType.toLowerCase().contains("text/html")) {
                    addEmailMessagesContentHtml(getContentWithoutMarkingAsRead(mailMessage), emailMessages, "将HTML内容添加到邮件正文{} {} {}", emailMessages.getAccountId(), emailMessages.getId());
                    return;
                }
                // 检查单部分邮件是否是附件
                String disposition = null;
                try {
                    if (mailMessage instanceof MimePart) {
                        disposition = mailMessage.getDisposition();
                    }
                } catch (MessagingException e) {
                    LOG.warn("获取邮件disposition失败: {}", e.getMessage());
                }
                if (disposition == null) {
                    return;
                }
                if (disposition.equalsIgnoreCase(Part.ATTACHMENT)) {
                    try (InputStream inputStream = mailMessage.getInputStream()) {
                        String fileName = mailMessage.getFileName();
                        // 获取contentId
                        String contentId = mailMessage.getHeader("Content-ID") != null ? mailMessage.getHeader("Content-ID")[0] : null;
                        if (contentId != null && contentId.startsWith("<") && contentId.endsWith(">")) {
                            contentId = contentId.substring(1, contentId.length() - 1);
                        }
                        writeBodyPartToFile(emailMessages, contentId, fileName, inputStream);
                    }
                }
            }
        } catch (Exception e) {
            LOG.warn("解析附件失败: {}", e.getMessage());
        }
    }

    private void parseAttachmentsFromMultipart(Multipart multipart, EmailMessages emailMessages) {
        Integer accountId = emailMessages.getAccountId();
        Long emailMessagesId = emailMessages.getId();
        String subject = emailMessages.getSubject();

        // 强制调试输出
        System.out.println("🔥 DEBUG: parseAttachmentsFromMultipart 被调用！");
        System.out.println("🔥 DEBUG: 邮件ID: " + emailMessagesId + ", 主题: " + subject);

        try {
            int count = multipart.getCount();
            System.out.println("🔥 DEBUG: 多部分邮件包含 " + count + " 个部分");

            for (int i = 0; i < count; i++) {
                System.out.println("🔥 DEBUG: --- 处理第 " + (i + 1) + " 部分 ---");
                BodyPart bodyPart = null;
                try {
                    bodyPart = multipart.getBodyPart(i);
                    String disposition = bodyPart.getDisposition();
                    String contentType = bodyPart.getContentType();

                    System.out.println("🔥 DEBUG: Content-Type: " + contentType);
                    System.out.println("🔥 DEBUG: Disposition: " + disposition);

                    // 1. 增强内容类型检测
                    String contentTypeLC = contentType.toLowerCase();
                    String fileName = getAttachmentFileName(bodyPart);

                    // 2. 更安全的内容获取
                    Object content = null;
                    try {
                        content = getBodyPartContentWithoutMarkingAsRead(bodyPart);
                    } catch (UnsupportedEncodingException e) {
                        // 处理编码问题，尝试使用不同编码
                        LOG.warn("附件内容编码问题，尝试使用默认编码: {}", e.getMessage());
                        content = streamToString(bodyPart.getInputStream());
                    }

                    // 3. 文本内容处理
                    // 优先检查是否为multipart类型，避免被text/html误判
                    if (contentTypeLC.startsWith("multipart/")) {
                        if (content instanceof Multipart) {
                            System.out.println("🔥 DEBUG: 发现嵌套的multipart，递归处理: " + contentType);
                            parseAttachmentsFromMultipart((Multipart) content, emailMessages);
                        } else {
                            System.out.println("🔥 DEBUG: Content-Type为multipart但实际对象不是Multipart: " +
                                (content != null ? content.getClass().getName() : "null"));
                        }
                        continue;
                    }

                    // 检查是否为PDF或其他二进制内容被误判为text/plain
                    if (contentTypeLC.contains("text/plain") && content != null) {
                        String contentStr = content.toString();
                        // 检查是否包含PDF标识符或其他二进制文件标识符
                        if (contentStr.startsWith("%PDF-") ||
                            contentStr.contains("%%EOF") ||
                            contentStr.startsWith("PK") || // ZIP文件
                            contentStr.startsWith("\u0089PNG") || // PNG文件
                            contentStr.startsWith("\u00FF\u00D8\u00FF") || // JPEG文件
                            contentStr.startsWith("GIF8") || // GIF文件
                            contentStr.length() > 10000) { // 超长文本可能是二进制内容

                            System.out.println("🔥 DEBUG: 检测到被误判为text/plain的二进制内容，长度: " + contentStr.length());
                            System.out.println("🔥 DEBUG: 内容开头: " + contentStr.substring(0, Math.min(50, contentStr.length())));

                            // 强制作为附件处理
                            if (fileName == null || fileName.isEmpty()) {
                                // 尝试从多个地方获取原始文件名
                                fileName = extractOriginalFileName(bodyPart);

                                if (fileName == null || fileName.isEmpty()) {
                                    // 根据内容推断文件扩展名
                                    if (contentStr.startsWith("%PDF-")) {
                                        fileName = "attachment_" + System.currentTimeMillis() + ".pdf";
                                    } else if (contentStr.startsWith("PK")) {
                                        fileName = "attachment_" + System.currentTimeMillis() + ".zip";
                                    } else if (contentStr.startsWith("\u0089PNG")) {
                                        fileName = "attachment_" + System.currentTimeMillis() + ".png";
                                    } else if (contentStr.startsWith("\u00FF\u00D8\u00FF")) {
                                        fileName = "attachment_" + System.currentTimeMillis() + ".jpg";
                                    } else if (contentStr.startsWith("GIF8")) {
                                        fileName = "attachment_" + System.currentTimeMillis() + ".gif";
                                    } else {
                                        fileName = "attachment_" + System.currentTimeMillis() + ".bin";
                                    }
                                }
                            }

                            System.out.println("🔥 DEBUG: ✅ 强制保存为附件: " + fileName);
                            LOG.info("检测到被误判的二进制内容，强制保存为附件: {} {} {} {}", accountId, emailMessagesId, subject, fileName);
                            writeBodyPartToFile(emailMessages, null, fileName, bodyPart.getInputStream());
                            continue;
                        }
                    }

                    if (contentTypeLC.contains("text/plain")) {
                        addEmailMessagesContentText(content, emailMessages, "已将Text内容添加到邮件正文{} {} {}", accountId, emailMessagesId);
                        continue;
                    }

                    if (contentTypeLC.contains("text/html")) {
                        addEmailMessagesContentHtml(content, emailMessages, "已将HTML内容添加到邮件正文{} {} {}", accountId, emailMessagesId);
                        continue;
                    }

                    // 4. 改进附件识别和处理
                    // 首先获取contentId，用于内联附件识别
                    String contentId = null;
                    try{
                        contentId = bodyPart.getHeader("Content-ID") != null ? bodyPart.getHeader("Content-ID")[0] : null;
                        System.out.println("🔥 DEBUG: 原始Content-ID: " + contentId);
                        if (contentId != null && contentId.startsWith("<") && contentId.endsWith(">")) {
                            contentId = contentId.substring(1, contentId.length() - 1);
                            System.out.println("🔥 DEBUG: 清理后Content-ID: " + contentId);
                        }
                    }catch(Exception e){
                        System.out.println("🔥 DEBUG: 获取Content-ID失败: " + e.getMessage());
                        LOG.error("获取邮件头部信息出错 {}, {}, {}", accountId, emailMessagesId, subject, e);
                    }

                    // 基于Content-Disposition、Content-Type、文件名和Content-ID识别附件
                    boolean isAttachment = false;
                    String attachmentReason = "";

                    System.out.println("🔥 DEBUG: 开始附件识别判断...");
                    System.out.println("🔥 DEBUG: fileName: " + fileName);

                    // 基于disposition判断
                    if (disposition != null &&
                            (disposition.equalsIgnoreCase(Part.ATTACHMENT) || disposition.equalsIgnoreCase(Part.INLINE))) {
                        isAttachment = true;
                        attachmentReason = "基于Disposition: " + disposition;
                        System.out.println("🔥 DEBUG: 通过Disposition识别为附件: " + disposition);
                    }
                    // 基于文件名判断
                    else if (fileName != null && !fileName.isEmpty()) {
                        isAttachment = true;
                        attachmentReason = "基于文件名: " + fileName;
                        System.out.println("🔥 DEBUG: 通过文件名识别为附件: " + fileName);
                    }
                    // 基于内容类型判断
                    else if (!contentTypeLC.startsWith("text/") && !contentTypeLC.startsWith("multipart/")) {
                        isAttachment = true;
                        attachmentReason = "基于内容类型: " + contentType;
                        System.out.println("🔥 DEBUG: 通过内容类型识别为附件: " + contentType);
                    }
                    // 特殊处理：如果有Content-ID，强制识别为内联附件
                    else if (contentId != null && !contentId.trim().isEmpty()) {
                        isAttachment = true;
                        attachmentReason = "基于Content-ID: " + contentId;
                        System.out.println("🔥 DEBUG: ✅ 通过Content-ID强制识别为内联附件: " + contentId);
                        LOG.info("检测到内联附件，Content-ID: {} {} {} {}", contentId, accountId, emailMessagesId, subject);
                    }

                    System.out.println("🔥 DEBUG: 最终判断结果 - 是否为附件: " + isAttachment + " (" + attachmentReason + ")");

                    // 5. 处理附件
                    if (isAttachment) {
                        System.out.println("🔥 DEBUG: ✅ 保存附件: " + (fileName != null ? fileName : "无文件名") + " (CID: " + contentId + ")");
                        LOG.info("识别为附件: {} {} {} {} ({})", accountId, emailMessagesId, subject,
                            fileName != null ? fileName : "无文件名", attachmentReason);
                        writeBodyPartToFile(emailMessages, contentId, fileName, bodyPart.getInputStream());
                    } else if (content != null) {
                        // 尝试将其他内容作为文本处理
                        System.out.println("🔥 DEBUG: 将非附件内容作为文本处理");
                        addEmailMessagesContentHtml(content, emailMessages,
                                "尝试将无法解读的附件，当成字符串内容添加到邮件正文{} {} {}", accountId, emailMessagesId);
                    }
                } catch (Exception e) {
                    LOG.error("解析多部分邮件的一部分失败: {} {} {} {}", accountId, emailMessagesId, subject, e.getMessage(), e);

                    // 6. 保存部分失败的附件信息用于后续恢复
                    if (bodyPart != null) {
                        try {
                            saveFailedAttachmentInfo(emailMessages, bodyPart, e);
                        } catch (Exception ex) {
                            LOG.error("记录失败的附件信息失败", ex);
                        }
                    }
                }
            }
        } catch (Exception e) {
            LOG.warn("解析多部分邮件失败: {} {} {} {}", accountId, emailMessagesId, subject, e.getMessage());
        }
    }

    /**
     * 写入邮件附件到文件，处理文件流方式
     */
    void writeBodyPartToFile(EmailMessages emailMessages, String contentId, String fileName, InputStream inputStream) {
        FileOutputStream outputStream = null;
        String attachmentPath = null;

        try {
            // 创建附件保存路径
            attachmentPath = EmailParsingService.createAttachmentPath(emailMessages, contentId);
            File attachmentDir = new File(attachmentPath);
            if (!attachmentDir.exists()) {
                attachmentDir.mkdirs();
            }
            
            // 使用文件名或生成唯一文件名
            if (fileName == null || fileName.trim().isEmpty()) {
                if (contentId != null && !contentId.trim().isEmpty()) {
                    // 对于内联附件，使用CID作为文件名基础
                    fileName = "inline_" + contentId.replaceAll("[^a-zA-Z0-9\\-]", "_");
                    LOG.info("为内联附件生成文件名: {} (基于CID: {})", fileName, contentId);
                } else {
                    fileName = "attachment_" + System.currentTimeMillis();
                }
            }
            
            // 确保文件名合法
            fileName = FileKit.sanitizeFileName(fileName);
            
            File file = new File(attachmentDir, fileName);
            if(file.exists()){
                fileName = UUID.randomUUID() + "_" + fileName;
                file = new File(attachmentDir, fileName);
            }

            // 写入文件
            outputStream = new FileOutputStream(file);
            
            byte[] buffer = new byte[8192];
            int bytesRead;
            long totalBytes = 0;
            
            while ((bytesRead = inputStream.read(buffer)) != -1) {
                outputStream.write(buffer, 0, bytesRead);
                totalBytes += bytesRead;
            }
            outputStream.flush();
            
            if (contentId != null && !contentId.trim().isEmpty()) {
                LOG.info("内联附件成功保存: 邮件ID={}, 文件名={}, CID={}, 大小={} 字节",
                    emailMessages.getId(), fileName, contentId, totalBytes);
            } else {
                LOG.info("普通附件成功保存: 邮件ID={}, 文件名={}, 大小={} 字节",
                    emailMessages.getId(), fileName, totalBytes);
            }

            // 保存附件元数据
            saveAttachmentMetadataSimple(emailMessages, attachmentPath, fileName, contentId, totalBytes);
        } catch (Exception e) {
            LOG.error("保存附件失败: {} {} {}", emailMessages.getId(), fileName, e.getMessage());
            saveAttachmentErrorToDB(emailMessages, fileName, contentId, e.getMessage());
        } finally {
            closeQuietly(outputStream);
        }
    }

    /**
     * 简化版元数据保存
     */
    private void saveAttachmentMetadataSimple(EmailMessages emailMessages, String attachmentPath, String fileName, String contentId, long fileSize) {
        try {
            EmailAttachments attachment = new EmailAttachments().dao().findFirst(
                    "SELECT * FROM email_attachments WHERE email_id = ? AND file_name = ?",
                    emailMessages.getId(), fileName);

            if (attachment == null) {
                attachment = new EmailAttachments();
                attachment.setEmailId(emailMessages.getId());
                attachment.setFileName(fileName);
                attachment.setOriginalFileName(fileName);
                attachment.setSafeFileName(fileName);
                attachment.setCreatedAt(new Date());
            }

            attachment.setCid(contentId);
            attachment.setContentIdFull(contentId);
            attachment.setPath(attachmentPath + "/" + fileName);
            attachment.setFileSize(fileSize);
            attachment.setStatus(2); // 已下载
            attachment.setUpdatedAt(new Date());

            if (attachment.getId() == null) {
                attachment.save();
            } else {
                attachment.update();
            }

            // 更新邮件记录的has_attachments字段
            updateEmailHasAttachments(emailMessages);

        } catch (Exception e) {
            LOG.error("保存附件元数据失败: {} {}", fileName, e.getMessage());
        }
    }

    /**
     * 将附件错误信息保存到数据库
     */
    private void saveAttachmentErrorToDB(EmailMessages emailMessages, String fileName, String contentId, String errorMsg) {
        try {
            // 创建或更新一条失败记录
            EmailAttachments attachment = new EmailAttachments().dao().findFirst(
                    "SELECT * FROM email_attachments WHERE email_id = ? AND file_name = ?",
                    emailMessages.getId(), fileName);

            if (attachment == null) {
                attachment = new EmailAttachments();
                attachment.setEmailId(emailMessages.getId());
                attachment.setFileName(fileName);
                attachment.setOriginalFileName(fileName);
                attachment.setCid(contentId);
                attachment.setStatus(3); // 下载失败
                attachment.setErrorMessage(StringUtils.left(errorMsg, 250)); // 截取错误信息避免过长
                attachment.setCreatedAt(new Date());
                attachment.setUpdatedAt(new Date());
                attachment.save();
            } else {
                attachment.setStatus(3); // 下载失败
                attachment.setErrorMessage(StringUtils.left(errorMsg, 250));
                attachment.setUpdatedAt(new Date());
                attachment.update();
            }

            // 更新邮件记录的has_attachments字段（即使失败也要更新状态）
            updateEmailHasAttachments(emailMessages);

        } catch (Exception ex) {
            LOG.error("保存附件错误信息到数据库失败", ex);
        }
    }

    /**
     * 更新邮件记录的has_attachments字段
     */
    private void updateEmailHasAttachments(EmailMessages emailMessages) {
        try {
            // 检查该邮件是否有附件记录
            Integer attachmentCount = Db.queryInt(
                    "SELECT COUNT(*) FROM email_attachments WHERE email_id = ? AND status IN (2, 5)",
                    emailMessages.getId());

            boolean hasAttachments = attachmentCount != null && attachmentCount > 0;

            // 只有当状态发生变化时才更新
            if (emailMessages.getHasAttachments() == null || emailMessages.getHasAttachments() != hasAttachments) {
                emailMessages.setHasAttachments(hasAttachments);
                emailMessages.setUpdatedAt(new Date());
                emailMessages.update();
                LOG.info("更新邮件附件状态: {} {} has_attachments={}",
                    emailMessages.getId(), emailMessages.getSubject(), hasAttachments);
            }
        } catch (Exception e) {
            LOG.error("更新邮件has_attachments字段失败: {} {}", emailMessages.getId(), e.getMessage());
        }
    }

    static void addEmailMessagesContentText(Object content, EmailMessages emailMessages, String s, Integer accountId, Long emailMessagesId) {
        // 将HTML内容添加到邮件正文
        if (content == null) {
            return;
        }
        String currentContent = emailMessages.getContentText();
        String contentString = content.toString();
        // 如果当前内容为空，直接设置，否则追加
        if (StringUtils.isEmpty(currentContent)) {
            emailMessages.setContentText(contentString);
        } else {
            // 检查是否已包含该内容，避免重复
            if (!currentContent.contains(contentString)) {
                emailMessages.setContentText(currentContent + "<br>" + contentString);
            }
        }

        // 更新邮件记录
        emailMessages.update();
        LOG.info(s, accountId, emailMessagesId, emailMessages.getSubject());
    }

    static void addEmailMessagesContentHtml(Object content, EmailMessages emailMessages, String s, Integer accountId, Long emailMessagesId) {
        // 将HTML内容添加到邮件正文
        if (content == null) {
            return;
        }
        String currentContent = emailMessages.getContentHtml();
        String contentString = content.toString();
        // 如果当前内容为空，直接设置，否则追加
        if (StringUtils.isEmpty(currentContent)) {
            emailMessages.setContentHtml(contentString);
        } else {
            // 检查是否已包含该内容，避免重复
            if (!currentContent.contains(contentString)) {
                emailMessages.setContentHtml(currentContent + "<hr>" + contentString);
            }
        }

        // 更新邮件记录
        emailMessages.update();
        LOG.info(s, accountId, emailMessagesId, emailMessages.getSubject());
    }

    @Override
    public Map<String, String> parseBody(Object message) {
        if (!(message instanceof Message)) {
            throw new IllegalArgumentException("Message must be a Jakarta Mail Message");
        }

        Map<String, String> result = new HashMap<>();
        StringBuilder contentHtmlBuilder = new StringBuilder();
        StringBuilder contentTextBuilder = new StringBuilder();
        Message mailMessage = (Message) message;

        try {
            String contentType = mailMessage.getContentType();
            LOG.info("解析邮件正文，内容类型: {}", contentType);
            if (contentType == null) {
                LOG.warn("邮件正文为空");
                return result;
            }
            
            // 使用PEEK方式获取内容，避免标记为已读
            Object content = getContentWithoutMarkingAsRead(mailMessage);
            
            // 如果是多部分邮件，查找文本部分
            if (contentType.toLowerCase().startsWith("multipart/")) {
                if (content instanceof Multipart) {
                    Multipart multipart = (Multipart) content;
                    LOG.info("处理多部分邮件，类型: {}, 部分数量: {}", contentType, multipart.getCount());
                    parseBodyFromMultipart(multipart, result);
                }
            } else if (contentType.toLowerCase().startsWith("text/plain")) {
                // 如果是文本邮件，直接获取内容
                if (content instanceof String) {
                    contentTextBuilder.append((String) content);
                    LOG.info("解析到文本内容，长度: {}", ((String) content).length());
                } else if (content instanceof InputStream) {
                    // 读取输入流
                    try (InputStream is = (InputStream) content) {
                        byte[] buffer = new byte[8192];
                        int bytesRead;
                        int totalRead = 0;
                        while ((bytesRead = is.read(buffer)) != -1) {
                            contentTextBuilder.append(new String(buffer, 0, bytesRead));
                            totalRead += bytesRead;
                        }
                        LOG.info("解析到文本流内容，长度: {}", totalRead);
                    }
                }
            } else if (contentType.toLowerCase().startsWith("text/html")) {
                // 如果是文本邮件，直接获取内容
                if (content instanceof String) {
                    contentHtmlBuilder.append((String) content);
                    LOG.info("解析到文本Html内容，长度: {}", ((String) content).length());
                } else if (content instanceof InputStream) {
                    // 读取输入流
                    try (InputStream is = (InputStream) content) {
                        byte[] buffer = new byte[8192];
                        int bytesRead;
                        int totalRead = 0;
                        while ((bytesRead = is.read(buffer)) != -1) {
                            contentHtmlBuilder.append(new String(buffer, 0, bytesRead));
                            totalRead += bytesRead;
                        }
                        LOG.info("解析到文本流Html内容，长度: {}", totalRead);
                    }
                }
            }

            // 检查最终结果
            if (contentTextBuilder.length() == 0 && contentHtmlBuilder.length() == 0) {
                LOG.warn("邮件正文解析结果为空");
            } else {
                LOG.info("邮件正文解析完成，html和text长度: {} {}", contentHtmlBuilder.length(), contentTextBuilder.length());
            }
        } catch (Exception e) {
            LOG.warn("解析邮件正文失败: {}", e.getMessage(), e);
        }

        addMapValue(result, "text", contentTextBuilder.toString(), "\n");
        addMapValue(result, "html", contentHtmlBuilder.toString(), "<hr>");
        return result;
    }
    
    /**
     * 使用PEEK方式获取邮件内容，避免标记为已读
     */
    Object getContentWithoutMarkingAsRead(Message message) throws MessagingException, IOException {
        try {
            // 尝试使用IMAP PEEK功能
            if (message instanceof MimeMessage) {
                MimeMessage mimeMessage = (MimeMessage) message;
                
                // 检查是否支持IMAP PEEK
                Folder folder = message.getFolder();
                if (folder != null && folder.getClass().getName().contains("IMAP")) {
                    
                    // 使用反射方式尝试PEEK
                    try {
                        // 尝试使用JavaMail的内部PEEK机制
                        // 通过设置系统属性来启用PEEK模式
                        String originalPeekValue = System.getProperty("mail.imap.peek");
                        System.setProperty("mail.imap.peek", "true");
                        
                        try {
                            // 获取内容
                            Object content = mimeMessage.getContent();
                            LOG.debug("成功使用PEEK方式获取邮件内容");
                            return content;
                        } finally {
                            // 恢复原始设置
                            if (originalPeekValue != null) {
                                System.setProperty("mail.imap.peek", originalPeekValue);
                            } else {
                                System.clearProperty("mail.imap.peek");
                            }
                        }
                    } catch (Exception e) {
                        LOG.debug("IMAP PEEK方式获取内容失败: {}", e.getMessage());
                    }
                }
            }
        } catch (Exception e) {
            LOG.debug("尝试PEEK方式失败: {}", e.getMessage());
        }
        
        // 如果PEEK方式失败，使用标准方式（可能会标记为已读）
        LOG.debug("使用标准方式获取邮件内容（注意：可能会标记为已读）");
        return message.getContent();
    }

    /**
     * 使用PEEK方式获取BodyPart内容，避免标记为已读
     */
    Object getBodyPartContentWithoutMarkingAsRead(BodyPart bodyPart) throws MessagingException, IOException {
        try {
            // 尝试使用IMAP PEEK功能
            if (bodyPart instanceof MimePart) {
                // 检查是否在IMAP环境中
                if (bodyPart.getClass().getName().contains("IMAP")) {
                    
                    // 使用反射方式尝试PEEK
                    try {
                        // 尝试使用JavaMail的内部PEEK机制
                        String originalPeekValue = System.getProperty("mail.imap.peek");
                        System.setProperty("mail.imap.peek", "true");
                        
                        try {
                            // 获取内容
                            Object content = bodyPart.getContent();
                            LOG.debug("成功使用PEEK方式获取BodyPart内容");
                            return content;
                        } finally {
                            // 恢复原始设置
                            if (originalPeekValue != null) {
                                System.setProperty("mail.imap.peek", originalPeekValue);
                            } else {
                                System.clearProperty("mail.imap.peek");
                            }
                        }
                    } catch (Exception e) {
                        LOG.debug("IMAP PEEK方式获取BodyPart内容失败: {}", e.getMessage());
                    }
                }
            }
        } catch (Exception e) {
            LOG.debug("尝试PEEK方式获取BodyPart内容失败: {}", e.getMessage());
        }
        
        // 如果PEEK方式失败，使用标准方式（可能会标记为已读）
        LOG.debug("使用标准方式获取BodyPart内容（注意：可能会标记为已读）");
        return bodyPart.getContent();
    }

    private void parseBodyFromMultipart(Multipart multipart, Map<String, String> result) {
        try {
            int count = multipart.getCount();
            String multipartContentType = "multipart/mixed";
            try {
                if (multipart instanceof MimePart) {
                    MimePart mimePart = (MimePart) multipart;
                    multipartContentType = mimePart.getContentType().toLowerCase();
                }
            } catch (Exception e) {
                LOG.warn("无法获取多部分内容类型: {}", e.getMessage());
            }
            StringBuilder contentTextBuilder = new StringBuilder(result.getOrDefault("text", ""));
            StringBuilder contentHtmlBuilder = new StringBuilder(result.getOrDefault("html", ""));

            LOG.info("处理多部分邮件，类型: {}, 部分数量: {}", multipartContentType, count);

            // 第一遍：收集所有内容
            for (int i = 0; i < count; i++) {
                try {
                    BodyPart bodyPart = multipart.getBodyPart(i);
                    String contentType = bodyPart.getContentType().toLowerCase();
                    String disposition = bodyPart.getDisposition();
                    LOG.info("处理第 {} 部分，内容类型: {}, 布局: {}", i + 1, contentType, disposition);

                    // 处理文本部分
                    if (contentType.startsWith("text/plain")) {
                        Object content = getBodyPartContentWithoutMarkingAsRead(bodyPart);
                        if (content instanceof String) {
                            String contentStr = (String) content;

                            // 检查是否为被误判的二进制内容
                            if (contentStr.startsWith("%PDF-") ||
                                contentStr.contains("%%EOF") ||
                                contentStr.startsWith("PK") || // ZIP文件
                                contentStr.startsWith("\u0089PNG") || // PNG文件
                                contentStr.startsWith("\u00FF\u00D8\u00FF") || // JPEG文件
                                contentStr.startsWith("GIF8") || // GIF文件
                                contentStr.length() > 50000) { // 超长文本可能是二进制内容

                                LOG.warn("检测到被误判为text/plain的二进制内容，跳过添加到正文，长度: {}", contentStr.length());
                                LOG.warn("内容开头: {}", contentStr.substring(0, Math.min(50, contentStr.length())));
                                continue; // 跳过这部分内容，不添加到正文
                            }

                            contentTextBuilder.append(contentStr);
                            contentTextBuilder.append("\n");
                            LOG.info("找到纯文本内容，长度: {}", contentStr.length());
                        } else if (content instanceof InputStream) {
                            // 读取输入流
                            try (InputStream is = (InputStream) content) {
                                byte[] buffer = new byte[8192];
                                int bytesRead;
                                int totalRead = 0;
                                StringBuilder tempBuilder = new StringBuilder();
                                while ((bytesRead = is.read(buffer)) != -1) {
                                    tempBuilder.append(new String(buffer, 0, bytesRead));
                                    totalRead += bytesRead;
                                    // 如果读取的内容过大，可能是二进制文件
                                    if (totalRead > 50000) {
                                        LOG.warn("检测到超大text/plain内容流，可能是二进制文件，跳过添加到正文，长度: {}", totalRead);
                                        break;
                                    }
                                }

                                String streamContent = tempBuilder.toString();
                                // 检查流内容是否为二进制
                                if (!streamContent.startsWith("%PDF-") &&
                                    !streamContent.startsWith("PK") &&
                                    totalRead <= 50000) {
                                    contentTextBuilder.append(streamContent);
                                    contentTextBuilder.append("\n");
                                    LOG.info("找到纯文本内容流，长度: {}", totalRead);
                                } else {
                                    LOG.warn("检测到二进制内容流，跳过添加到正文");
                                }
                            }
                        }
                    } else if (contentType.startsWith("text/html")) {
                        Object content = getBodyPartContentWithoutMarkingAsRead(bodyPart);
                        if (content instanceof String) {
                            contentHtmlBuilder.append((String) content);
                            LOG.info("找到HTML内容，长度: {}", ((String) content).length());
                        } else if (content instanceof InputStream) {
                            // 读取输入流
                            try (InputStream is = (InputStream) content) {
                                byte[] buffer = new byte[8192];
                                int bytesRead;
                                int totalRead = 0;
                                while ((bytesRead = is.read(buffer)) != -1) {
                                    contentHtmlBuilder.append(new String(buffer, 0, bytesRead));
                                    totalRead += bytesRead;
                                }
                                LOG.info("找到HTML内容流，长度: {}", totalRead);
                            }
                        }
                    } else if (contentType.startsWith("multipart/")) {
                        // 递归解析嵌套的多部分内容
                        Object content = getBodyPartContentWithoutMarkingAsRead(bodyPart);
                        if (content instanceof Multipart) {
                            LOG.info("递归处理嵌套的多部分内容，类型: {}", contentType);
                            parseBodyFromMultipart((Multipart) content, result);
                        }
                    } else if (disposition != null && disposition.equalsIgnoreCase(Part.INLINE)) {
                        // 如果是文本内容，则尝试读取
                        if (contentType.startsWith("text/")) {
                            Object content = getBodyPartContentWithoutMarkingAsRead(bodyPart);
                            if (content instanceof String) {
                                if (contentType.startsWith("text/html")) {
                                    contentHtmlBuilder.append((String) content);
                                    LOG.info("找到内嵌(inline)HTML内容，长度: {}", ((String) content).length());
                                } else {
                                    contentTextBuilder.append((String) content);
                                    LOG.info("找到内嵌(inline)纯文本内容，长度: {}", ((String) content).length());
                                }
                            } else if (content instanceof InputStream) {
                                // 读取输入流
                                try (InputStream is = (InputStream) content) {
                                    byte[] buffer = new byte[8192];
                                    int bytesRead;
                                    int totalRead = 0;
                                    StringBuilder tempContent = new StringBuilder();
                                    while ((bytesRead = is.read(buffer)) != -1) {
                                        tempContent.append(new String(buffer, 0, bytesRead));
                                        totalRead += bytesRead;
                                    }

                                    if (contentType.startsWith("text/html")) {
                                        contentHtmlBuilder.append(tempContent);
                                        LOG.info("找到内嵌(inline)HTML内容流，长度: {}", totalRead);
                                    } else {
                                        contentTextBuilder.append(tempContent);
                                        LOG.info("找到内嵌(inline)纯文本内容流，长度: {}", totalRead);
                                    }
                                }
                            }
                        }
                    }
                } catch (Exception e) {
                    LOG.warn("解析多部分邮件的一部分失败: {}", e.getMessage());
                }
            }
            addMapValue(result, "text", contentTextBuilder.toString(), "\n");
            addMapValue(result, "html", contentHtmlBuilder.toString(), "<hr>");
            LOG.info("最终邮件正文html, text长度: {} {}", contentHtmlBuilder.length(), contentTextBuilder.length());
        } catch (Exception e) {
            LOG.warn("解析多部分邮件失败: " + e.getMessage());
        }
    }

    @Override
    public boolean canParse(Object message) {
        return message instanceof Message;
    }

    @Override
    public String getParserName() {
        return "Jakarta Mail Parser";
    }

    @Override
    public InputStream getRawInputStream(Object message) throws Exception {
        if (!(message instanceof Message)) {
            throw new IllegalArgumentException("Message must be a Jakarta Mail Message");
        }

        Message mailMessage = (Message) message;
        if (mailMessage instanceof MimeMessage) {
            return mailMessage.getInputStream();
        } else {
            throw new UnsupportedOperationException("Cannot get raw input stream from non-MimeMessage");
        }
    }

    /**
     * 获取附件文件名
     *
     * @param bodyPart 邮件正文部分
     * @return 文件名
     */
    String getAttachmentFileName(BodyPart bodyPart) throws MessagingException {
        String fileName = bodyPart.getFileName();
        if (fileName == null) {
            return null;
        }

        // 解码文件名
        try {
            fileName = MimeUtility.decodeText(fileName);
        } catch (UnsupportedEncodingException e) {
            LOG.warn("解码附件文件名失败: {}", e.getMessage());
        }

        return fileName;
    }

    /**
     * 从多个地方尝试提取原始文件名
     * 用于处理被误判为text/plain的附件
     *
     * @param bodyPart 邮件正文部分
     * @return 提取到的文件名，如果没有找到则返回null
     */
    private String extractOriginalFileName(BodyPart bodyPart) {
        try {
            // 1. 首先尝试标准的getFileName方法
            String fileName = bodyPart.getFileName();
            if (fileName != null && !fileName.trim().isEmpty()) {
                try {
                    fileName = MimeUtility.decodeText(fileName);
                    LOG.info("从标准getFileName获取到文件名: {}", fileName);
                    return fileName;
                } catch (Exception e) {
                    LOG.warn("解码文件名失败，使用原始文件名: {}", fileName);
                    return fileName;
                }
            }

            // 2. 尝试从Content-Disposition头获取
            String[] dispositionHeaders = bodyPart.getHeader("Content-Disposition");
            if (dispositionHeaders != null && dispositionHeaders.length > 0) {
                for (String disposition : dispositionHeaders) {
                    fileName = extractFileNameFromDisposition(disposition);
                    if (fileName != null && !fileName.trim().isEmpty()) {
                        LOG.info("从Content-Disposition头获取到文件名: {}", fileName);
                        return fileName;
                    }
                }
            }

            // 3. 尝试从Content-Type头的name参数获取
            String[] contentTypeHeaders = bodyPart.getHeader("Content-Type");
            if (contentTypeHeaders != null && contentTypeHeaders.length > 0) {
                for (String contentType : contentTypeHeaders) {
                    fileName = extractFileNameFromContentType(contentType);
                    if (fileName != null && !fileName.trim().isEmpty()) {
                        LOG.info("从Content-Type头获取到文件名: {}", fileName);
                        return fileName;
                    }
                }
            }

            // 4. 尝试从其他可能的头字段获取
            String[] nameHeaders = bodyPart.getHeader("name");
            if (nameHeaders != null && nameHeaders.length > 0) {
                fileName = nameHeaders[0];
                if (fileName != null && !fileName.trim().isEmpty()) {
                    try {
                        fileName = MimeUtility.decodeText(fileName);
                        LOG.info("从name头获取到文件名: {}", fileName);
                        return fileName;
                    } catch (Exception e) {
                        LOG.info("从name头获取到文件名（未解码）: {}", fileName);
                        return fileName;
                    }
                }
            }

            LOG.debug("无法从任何地方提取到文件名");
            return null;

        } catch (Exception e) {
            LOG.warn("提取原始文件名时发生错误: {}", e.getMessage());
            return null;
        }
    }

    /**
     * 从Content-Disposition头中提取文件名
     */
    private String extractFileNameFromDisposition(String disposition) {
        if (disposition == null) return null;

        try {
            // 查找filename参数
            String[] parts = disposition.split(";");
            for (String part : parts) {
                part = part.trim();
                if (part.toLowerCase().startsWith("filename=")) {
                    String fileName = part.substring(9).trim();
                    // 移除引号
                    if (fileName.startsWith("\"") && fileName.endsWith("\"")) {
                        fileName = fileName.substring(1, fileName.length() - 1);
                    }
                    // 尝试解码
                    try {
                        return MimeUtility.decodeText(fileName);
                    } catch (Exception e) {
                        return fileName;
                    }
                }
                // 也检查filename*参数（RFC 2231格式）
                if (part.toLowerCase().startsWith("filename*=")) {
                    String fileName = part.substring(10).trim();
                    return decodeRFC2231FileName(fileName);
                }
            }
        } catch (Exception e) {
            LOG.warn("解析Content-Disposition失败: {}", e.getMessage());
        }
        return null;
    }

    /**
     * 从Content-Type头中提取文件名
     */
    private String extractFileNameFromContentType(String contentType) {
        if (contentType == null) return null;

        try {
            // 查找name参数
            String[] parts = contentType.split(";");
            for (String part : parts) {
                part = part.trim();
                if (part.toLowerCase().startsWith("name=")) {
                    String fileName = part.substring(5).trim();
                    // 移除引号
                    if (fileName.startsWith("\"") && fileName.endsWith("\"")) {
                        fileName = fileName.substring(1, fileName.length() - 1);
                    }
                    // 尝试解码
                    try {
                        return MimeUtility.decodeText(fileName);
                    } catch (Exception e) {
                        return fileName;
                    }
                }
            }
        } catch (Exception e) {
            LOG.warn("解析Content-Type失败: {}", e.getMessage());
        }
        return null;
    }

    /**
     * 解码RFC 2231格式的文件名
     */
    private String decodeRFC2231FileName(String encodedFileName) {
        try {
            // RFC 2231格式: charset'language'encoded-value
            if (encodedFileName.contains("'")) {
                String[] parts = encodedFileName.split("'", 3);
                if (parts.length >= 3) {
                    String charset = parts[0];
                    String encodedValue = parts[2];

                    // URL解码
                    String decodedValue = java.net.URLDecoder.decode(encodedValue, charset);
                    return decodedValue;
                }
            }
            return encodedFileName;
        } catch (Exception e) {
            LOG.warn("解码RFC2231文件名失败: {}", e.getMessage());
            return encodedFileName;
        }
    }


    /**
     * 安全关闭流
     */
    private void closeQuietly(Closeable closeable) {
        if (closeable != null) {
            try {
                closeable.close();
            } catch (IOException e) {
                // 忽略关闭异常
                LOG.debug("关闭资源失败", e);
            }
        }
    }

    /**
     * 将输入流转换为字符串
     *
     * @param inputStream 输入流
     * @return 字符串内容
     */
    private String streamToString(InputStream inputStream) throws IOException {
        if (inputStream == null) {
            return "";
        }

        StringBuilder stringBuilder = new StringBuilder();
        byte[] buffer = new byte[8192];
        int bytesRead;

        while ((bytesRead = inputStream.read(buffer)) != -1) {
            stringBuilder.append(new String(buffer, 0, bytesRead));
        }

        return stringBuilder.toString();
    }

    /**
     * 保存失败的附件信息，用于后续恢复
     */
    void saveFailedAttachmentInfo(EmailMessages emailMessages, BodyPart bodyPart, Exception e) {
        try {
            String fileName = "unknown";
            try {
                fileName = bodyPart.getFileName();
            } catch (Exception ex) {
                // 忽略获取文件名异常
            }

            String failLogPath = EmailParsingService.createAttachmentPath(emailMessages, null) + File.separator + "failed_attachments.log";
            String errorInfo = "时间: " + new Date() + "\n" +
                    "文件: " + fileName + "\n" +
                    "错误: " + e.getMessage() + "\n" +
                    "-------------------\n";

            FileUtils.writeStringToFile(new File(failLogPath), errorInfo, "UTF-8", true);
        } catch (Exception ex) {
            LOG.error("保存附件失败信息异常", ex);
        }
    }

}
