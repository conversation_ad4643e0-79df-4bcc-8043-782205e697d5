package cn.jbolt.admin.emails;

import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.jsoup.select.Elements;
import org.junit.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 邮件图片CID转换测试
 * 测试相对路径图片转换为CID格式的功能
 */
public class EmailImageCidTest {
    
    private static final Logger log = LoggerFactory.getLogger(EmailImageCidTest.class);
    
    @Test
    public void testRelativePathImageProcessing() {
        // 模拟包含相对路径图片的HTML内容
        String htmlContent = "<html><body>" +
            "<p>这是一封测试邮件</p>" +
            "<img src=\"../../upload/email/image/20250801/55c00f9580824948bb2e96636959b5e3.jpg&random=3\" alt=\"测试图片1\">" +
            "<img src=\"/upload/email/image/20250801/another_image.png\" alt=\"测试图片2\">" +
            "<img src=\"../upload/files/document.pdf\" alt=\"非图片文件\">" +
            "<img src=\"http://external.com/image.jpg\" alt=\"外部图片\">" +
            "</body></html>";
        
        log.info("原始HTML内容: {}", htmlContent);
        
        // 解析HTML
        Document doc = Jsoup.parse(htmlContent);
        Elements imgElements = doc.select("img[src]");
        
        log.info("找到 {} 个图片标签", imgElements.size());
        
        for (Element img : imgElements) {
            String src = img.attr("src");
            log.info("处理图片: {}", src);
            
            // 跳过外部URL
            if (src.startsWith("http://") || src.startsWith("https://") || src.startsWith("cid:")) {
                log.info("跳过外部URL或已处理的CID: {}", src);
                continue;
            }
            
            String cleanSrc = src;
            
            // 处理URL参数（如&random=3）
            if (cleanSrc.contains("&")) {
                cleanSrc = cleanSrc.substring(0, cleanSrc.indexOf("&"));
                log.info("移除URL参数后: {}", cleanSrc);
            }
            if (cleanSrc.contains("?")) {
                cleanSrc = cleanSrc.substring(0, cleanSrc.indexOf("?"));
                log.info("移除查询参数后: {}", cleanSrc);
            }
            
            // 模拟路径处理逻辑
            String normalizedPath = cleanSrc;
            if (cleanSrc.startsWith("../")) {
                // 处理相对路径，如 ../../upload/email/image/xxx.jpg
                while (normalizedPath.startsWith("../")) {
                    normalizedPath = normalizedPath.substring(3);
                }
                // 如果规范化后的路径不以/开头，添加/
                if (!normalizedPath.startsWith("/")) {
                    normalizedPath = "/" + normalizedPath;
                }
                log.info("规范化相对路径: {} -> {}", cleanSrc, normalizedPath);
            }
            
            // 生成CID
            String cid;
            if (src.startsWith("/upload/")) {
                cid = src.substring("/upload/".length()).replace("/", "_");
                // 移除URL参数
                if (cid.contains("&")) {
                    cid = cid.substring(0, cid.indexOf("&"));
                }
                if (cid.contains("?")) {
                    cid = cid.substring(0, cid.indexOf("?"));
                }
            } else if (cleanSrc.contains("/upload/")) {
                // 处理相对路径中包含upload的情况
                String uploadPath = cleanSrc.substring(cleanSrc.indexOf("/upload/") + "/upload/".length());
                cid = uploadPath.replace("/", "_");
            } else {
                // 其他情况使用文件名作为CID
                String fileName = cleanSrc.substring(cleanSrc.lastIndexOf("/") + 1);
                cid = fileName;
            }
            
            log.info("生成CID: {} -> cid:{}", src, cid);
            
            // 模拟设置CID
            img.attr("src", "cid:" + cid);
        }
        
        String processedHtml = doc.html();
        log.info("处理后的HTML: {}", processedHtml);
        
        // 验证结果
        Document processedDoc = Jsoup.parse(processedHtml);
        Elements processedImgs = processedDoc.select("img[src^=cid:]");
        
        log.info("转换为CID格式的图片数量: {}", processedImgs.size());
        
        for (Element img : processedImgs) {
            String cidSrc = img.attr("src");
            log.info("CID图片: {}", cidSrc);
        }
    }
    
    @Test
    public void testCidGeneration() {
        // 测试各种路径的CID生成
        String[] testPaths = {
            "../../upload/email/image/20250801/55c00f9580824948bb2e96636959b5e3.jpg&random=3",
            "/upload/email/image/20250801/another_image.png",
            "../upload/files/document.pdf",
            "upload/test/image.jpg",
            "image.png"
        };
        
        for (String path : testPaths) {
            String cid = generateCid(path);
            log.info("路径: {} -> CID: {}", path, cid);
        }
    }
    
    private String generateCid(String src) {
        String cleanSrc = src;
        
        // 处理URL参数
        if (cleanSrc.contains("&")) {
            cleanSrc = cleanSrc.substring(0, cleanSrc.indexOf("&"));
        }
        if (cleanSrc.contains("?")) {
            cleanSrc = cleanSrc.substring(0, cleanSrc.indexOf("?"));
        }
        
        String cid;
        if (src.startsWith("/upload/")) {
            cid = src.substring("/upload/".length()).replace("/", "_");
            // 移除URL参数
            if (cid.contains("&")) {
                cid = cid.substring(0, cid.indexOf("&"));
            }
            if (cid.contains("?")) {
                cid = cid.substring(0, cid.indexOf("?"));
            }
        } else if (cleanSrc.contains("/upload/")) {
            // 处理相对路径中包含upload的情况
            String uploadPath = cleanSrc.substring(cleanSrc.indexOf("/upload/") + "/upload/".length());
            cid = uploadPath.replace("/", "_");
        } else {
            // 其他情况使用文件名作为CID
            String fileName = cleanSrc.substring(cleanSrc.lastIndexOf("/") + 1);
            cid = fileName;
        }
        
        return cid;
    }
}
